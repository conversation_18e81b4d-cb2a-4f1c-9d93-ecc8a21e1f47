"use client";
import { Link, usePathname } from "@/i18n/navigation";
import { TabNav } from "@radix-ui/themes";
import classNames from "classnames";
import Image from "next/image";
import style from "./index.module.scss";
import type { LinkConfig } from "./utils";

export const AppNav = ({
  linkConfig,
  action,
}: {
  linkConfig: LinkConfig[];
  action?: React.ReactNode;
}) => {
  const pathname = usePathname();

  const isKyc = pathname === "/kyc";

  return (
    <div className="flex gap-6 h-18 md:h-15 items-center justify-between">
      <div className="flex items-center gap-6">
        <Link href="/">
          <Image src="/odc-logo-bw.png" alt="Nav logo" width={65} height={32} />
        </Link>
        {isKyc ? null : (
          <div className="hidden md:block">
            <TabNav.Root color="gray" className={classNames(style.nav)}>
              {linkConfig.map(({ href, label }) => {
                const active = pathname === href;

                return (
                  <TabNav.Link
                    key={href}
                    asChild
                    active={active}
                    className={style.tab}
                  >
                    <Link key={href} href={href}>
                      <span>{label}</span>
                    </Link>
                  </TabNav.Link>
                );
              })}
            </TabNav.Root>
          </div>
        )}
      </div>

      {isKyc ? null : <div>{action}</div>}
    </div>
  );
};
