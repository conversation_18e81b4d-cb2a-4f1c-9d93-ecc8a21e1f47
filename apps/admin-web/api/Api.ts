/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import {
  DepositReviewRequest,
  KycReviewRequest,
  ResponseResultListKycOperatorReasonDTO,
  ResponseResultObject,
  ResponseResultOperatorDTO,
  ResponseResultPagedKycVerificationSearchResponse,
  ResponseResultPagedUserSummaryResponse,
  ResponseResultPortfolioOverviewResponse,
  ResponseResultPresignedURLResponse,
  ResponseResultVoid,
} from "./data-contracts";
import { ContentType, HttpClient, RequestParams } from "./http-client";

export class Api<
  SecurityDataType = unknown,
> extends HttpClient<SecurityDataType> {
  /**
   * No description
   *
   * @tags KYC Review
   * @name ReviewKyc
   * @request POST:/api/v1/kyc/review
   */
  reviewKyc = (data: KycReviewRequest, params: RequestParams = {}) =>
    this.request<
      ResponseResultVoid,
      (ResponseResultVoid | ResponseResultObject) | ResponseResultObject
    >({
      path: `/api/v1/kyc/review`,
      method: "POST",
      body: data,
      type: ContentType.Json,
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags V1DepositReviewController
   * @name ReviewDeposit
   * @request POST:/api/v1/deposits/review
   */
  reviewDeposit = (data: DepositReviewRequest, params: RequestParams = {}) =>
    this.request<
      ResponseResultVoid,
      (ResponseResultVoid | ResponseResultObject) | ResponseResultObject
    >({
      path: `/api/v1/deposits/review`,
      method: "POST",
      body: data,
      type: ContentType.Json,
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags V1UserManagementController
   * @name GetUsers
   * @request GET:/api/v1/user-management/users
   */
  getUsers = (
    query?: {
      publicId?: string;
      email?: string;
      /**
       * 1-indexed page number
       * @format int32
       * @min 1
       * @default 1
       */
      page?: number;
      /**
       * @format int32
       * @min 1
       * @max 100
       * @default 20
       */
      pageSize?: number;
    },
    params: RequestParams = {},
  ) =>
    this.request<
      ResponseResultPagedUserSummaryResponse,
      (ResponseResultVoid | ResponseResultObject) | ResponseResultObject
    >({
      path: `/api/v1/user-management/users`,
      method: "GET",
      query: query,
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags V1OperatorController
   * @name GetOperatorProfile
   * @request GET:/api/v1/operator/profile
   */
  getOperatorProfile = (params: RequestParams = {}) =>
    this.request<
      ResponseResultOperatorDTO,
      (ResponseResultVoid | ResponseResultObject) | ResponseResultObject
    >({
      path: `/api/v1/operator/profile`,
      method: "GET",
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags KYC Review
   * @name SearchKycVerifications
   * @request GET:/api/v1/kyc/verifications
   */
  searchKycVerifications = (
    query?: {
      publicId?: string;
      email?: string;
      status?: "PENDING" | "APPROVED" | "REJECTED" | "RESTRICTED";
      /**
       * 1-indexed page number
       * @format int32
       * @min 1
       * @default 1
       */
      page?: number;
      /**
       * @format int32
       * @min 1
       * @max 100
       * @default 20
       */
      pageSize?: number;
    },
    params: RequestParams = {},
  ) =>
    this.request<
      ResponseResultPagedKycVerificationSearchResponse,
      (ResponseResultVoid | ResponseResultObject) | ResponseResultObject
    >({
      path: `/api/v1/kyc/verifications`,
      method: "GET",
      query: query,
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags KYC Review
   * @name GetReviewReasons
   * @request GET:/api/v1/kyc/review/reasons
   */
  getReviewReasons = (
    query: {
      action: "APPROVE" | "REJECT" | "RESTRICT";
    },
    params: RequestParams = {},
  ) =>
    this.request<
      ResponseResultListKycOperatorReasonDTO,
      (ResponseResultVoid | ResponseResultObject) | ResponseResultObject
    >({
      path: `/api/v1/kyc/review/reasons`,
      method: "GET",
      query: query,
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags KYC Review
   * @name GetDocumentPresignedUrl
   * @request GET:/api/v1/kyc/documents/url
   */
  getDocumentPresignedUrl = (
    query: {
      /** @minLength 1 */
      fileKey: string;
    },
    params: RequestParams = {},
  ) =>
    this.request<
      ResponseResultPresignedURLResponse,
      (ResponseResultVoid | ResponseResultObject) | ResponseResultObject
    >({
      path: `/api/v1/kyc/documents/url`,
      method: "GET",
      query: query,
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags V1DashboardController
   * @name GetPortfolioOverview
   * @request GET:/api/v1/dashboard
   */
  getPortfolioOverview = (params: RequestParams = {}) =>
    this.request<
      ResponseResultPortfolioOverviewResponse,
      (ResponseResultVoid | ResponseResultObject) | ResponseResultObject
    >({
      path: `/api/v1/dashboard`,
      method: "GET",
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags V1AuthController
   * @name Logout
   * @request GET:/api/v1/auth/logout
   */
  logout = (params: RequestParams = {}) =>
    this.request<
      void,
      (ResponseResultVoid | ResponseResultObject) | ResponseResultObject
    >({
      path: `/api/v1/auth/logout`,
      method: "GET",
      ...params,
    });
  /**
   * No description
   *
   * @tags V1AuthController
   * @name Login
   * @request GET:/api/v1/auth/login
   */
  login = (params: RequestParams = {}) =>
    this.request<
      void,
      (ResponseResultVoid | ResponseResultObject) | ResponseResultObject
    >({
      path: `/api/v1/auth/login`,
      method: "GET",
      ...params,
    });
}
